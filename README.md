# Jenkins Pipelines

## Intro

This project contains the common Jenkins pipelines to make them reusable. 
It is mainly based on this [document](https://jenkins.io/doc/book/pipeline/shared-libraries/) but some things might be repeated here.

## Jenkins Configuration

In Jenkins navigate to:

`Man<PERSON> Jenkins > Configure System > Global Pipeline Libraries`

Add the following configuration:
```
Name: jenkins-pipelines
Default version: master
Load implicitly: no
Allow default version to be overridden: yes
Include @Library changes in job recent changes: yes

Retrieval method: Modern SCM

Source Code Management: Git
Project Repository: ******************:newsites/jenkins-pipelines.git
Credentials: ***
```

## Jenkinsfile

In your Jenkinsfile first you have to include the library you configured above with `@Library('your-library-name')`.
Then you can use the different pipelines under `/vars` in your Jenkinsfile.

An example for a `springBootPipeline`:

```
// Jenkinsfile
@Library('jenkins-pipelines') _

springBootPipeline(
        "ids-process",                                                      // app name
        "*ids-process.git",                                                 // git url
        ["<EMAIL>", "<EMAIL>"]   // email list
)
```

## Pipelines

#####A list of Pipelines:
- Spring Boot Pipeline
- (Generic) Ansible Pipeline
- Angular 8 Pipeline

##### Common Requirements
- ansible

### Spring Boot Pipeline

##### Requirements
- maven
- a jdk called `java11` added to Jenkins
- newman

### (Generic) Ansible Pipeline


### Angular 8 Pipeline

##### Requirements
- maven
- node.js

