# Katalon KRE Pipeline Test Setup Guide

## Environment Details
- **KRE VM**: `svlkatalkre101p`
- **Katalon User**: `katalon101p`
- **Ansible User**: `ansible`

## Prerequisites Checklist

### On Jenkins VM:
- [ ] Ansible installed and configured
- [ ] SSH access to KRE VM configured
- [ ] Jenkins shared library with this pipeline available
- [ ] Git repository with Katalon test project

### On KRE VM (svlkatalkre101p):
- [ ] `ansible` user exists with sudo privileges
- [ ] `katalon101p` user exists
- [ ] SSH key-based authentication configured for `ansible` user
- [ ] Working directory `/data/app/kre/working_dir` exists and is writable by `katalon101p`
- [ ] Katalon Runtime Engine installed (for actual testing)

## Setup Steps

### 1. SSH Key Configuration
```bash
# On Jenkins VM, generate SSH key if not exists
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa

# Copy public key to KRE VM
ssh-copy-id ansible@svlkatalkre101p

# Test SSH connection
ssh ansible@svlkatalkre101p "sudo -u katalon101p whoami"
```

### 2. KRE VM Directory Setup
```bash
# On KRE VM, create working directory
sudo mkdir -p /data/app/kre/working_dir
sudo chown katalon101p:katalon101p /data/app/kre/working_dir
sudo chmod 755 /data/app/kre/working_dir
```

### 3. Test Ansible Connection
```bash
# From your project directory
cd ansible
ansible -i inventory kre_servers -m ping
ansible -i inventory kre_servers -m shell -a "sudo -u katalon101p whoami"
```

### 4. Jenkins Pipeline Configuration
Create a new Jenkins job with:
- **Pipeline script from SCM**
- **Git repository** containing your Katalon test project
- **Pipeline script path**: `Jenkinsfile` (see example below)

## Test Jenkinsfile Example
```groovy
@Library('your-shared-library') _

pipeline {
    agent any
    stages {
        stage('Test KRE Pipeline') {
            steps {
                script {
                    katalonKREPipeline(
                        'katalon-test-project',
                        'https://your-git-repo/katalon-test-project.git',
                        '<EMAIL>'
                    )
                }
            }
        }
    }
}
```

## Pipeline Parameters for Testing
- **target_environment**: `test`
- **target_branch**: `master` or your test branch
- **vault_password**: `test123` (or your test password)
- **limit_hosts**: (leave empty)
- **kre_working_dir**: `/data/app/kre/working_dir`
- **cleanup_after_execution**: `true`

## Troubleshooting

### Common Issues:
1. **SSH Connection Failed**
   - Check SSH key configuration
   - Verify `ansible` user has sudo privileges
   - Test manual SSH connection

2. **Permission Denied on KRE VM**
   - Verify `katalon101p` user exists
   - Check working directory permissions
   - Ensure `ansible` user can sudo to `katalon101p`

3. **Ansible Playbook Errors**
   - Check inventory file syntax
   - Verify hostname resolution
   - Test individual playbook execution

### Manual Testing Commands:
```bash
# Test individual playbooks
ansible-playbook -i inventory kre-project-transfer.yml -e "host_env=test kre_working_directory=/data/app/kre/working_dir project_source_path=/path/to/test/project"

ansible-playbook -i inventory kre-test-execution.yml -e "host_env=test kre_working_directory=/data/app/kre/working_dir"

ansible-playbook -i inventory kre-results-collection.yml -e "host_env=test kre_working_directory=/data/app/kre/working_dir results_destination=/tmp/test-results"

ansible-playbook -i inventory kre-cleanup.yml -e "host_env=test kre_working_directory=/data/app/kre/working_dir"
```

## Next Steps
1. Complete prerequisites setup
2. Test SSH connectivity
3. Run individual Ansible playbooks manually
4. Create test Jenkins job
5. Execute full pipeline test
6. Review results and logs
7. Implement actual KRE CLI commands in test execution playbook
