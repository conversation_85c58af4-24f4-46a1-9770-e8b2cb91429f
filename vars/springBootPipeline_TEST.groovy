import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils
import com.degroofpetercam.jenkins.PostmanEnvironment

def call(appName, gitUrl, emailList, collectionId) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test', 'acc','acc2','emr', 'sandbox', 'prod'], description: 'Specify the target environment')
            booleanParam(name: 'full_pipeline', defaultValue: true, description: 'When checked, all the pipeline steps will be run ( compile, deploy, ...). If not , only the last deployment step will be run.')
            booleanParam(name: 'run_api_tests', defaultValue: false, description: 'To run API tests.')
            booleanParam(name: 'kong_expose_service', defaultValue: false, description: 'Expose Service on Kong.')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
            string(defaultValue: "", description: 'If you want to limit the deployment to certain hosts of the environment, leave empty to deploy on all nodes.?', name: 'limit_hosts')
        }
        environment {
            def vault_password_file = "ansible/vault-${env.target_environment}.\$\$.txt"
            def limit_hosts = Utils.parseLimitHosts("${env.limit_hosts}")
            def email_list = EmailService.parseEmailList(emailList)
            def app_name = Utils.parseString(appName)
            def postman_env_uid = PostmanEnvironment.getEnvironment("${env.target_environment}")
            def collection_id = Utils.parseString(collectionId)
        }
        tools {
            jdk 'java11'
        }
        stages {
            stage('Deployment notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                        RELEASE_VERSION = sh (
                            script: 'mvn org.apache.maven.plugins:maven-help-plugin:2.1.1:evaluate -Dexpression=project.version | sed -n -e \'/^\\[.*\\]/ !{ /^[0-9]/ { p; q } }\'',
                            returnStdout: true
                        ).trim()
						wrap([$class: 'BuildUser']) {
                            currentBuild.displayName = "$target_environment - ${RELEASE_VERSION}"
    						currentBuild.description = "By ${BUILD_USER}"
                        }
                    }
                }
            }
            stage ('Compile') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'mvn clean compile -DskipTests'
                    }
                }
            }
            stage ('Unit Tests') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'mvn test -DskipITs'
                    }
                }
            }
            stage ('Static Code Analysis') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        withSonarQubeEnv('Sonar') {
                            sh 'mvn verify org.sonarsource.scanner.maven:sonar-maven-plugin:3.7.0.1746:sonar'
                        }
                    }
                }
            }
            stage("Quality Gate") {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        timeout(time: 1, unit: 'MINUTES') {
                            script {
                                def qg = waitForQualityGate()
                                if (qg.status != 'OK') {
                                   error "Pipeline aborted due to quality gate failure: ${qg.status}"
                                }
                            }
                        }
                    }
                }
            }
            stage ('Package') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'mvn package -DskipTests'
                    }
                }
            }
            stage('Push to Nexus') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'mvn deploy -DskipTests'
                    }
                }
            }
            stage ('Deployment') {
                steps {
                    script {
                        sh """
                        set +x
                        echo \$vault_password > \$vault_password_file
                        set -x
                       """
                        sh "ansible-galaxy install -r ansible/requirements.yml --force --roles-path=ansible/roles"
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook " +
                                " -i ansible/inventory --vault-password-file=\$vault_password_file " +
                                " -e host_env=\$target_environment ansible/deploy-springboot.yml \$limit_hosts"

                    }
                }
            }
            stage ('Kong Expose Service') {
                when {
                    expression { params.kong_expose_service }
                }
                steps {
                    script {
                        sh """
                        set +x
                        echo \$vault_password > \$vault_password_file
                        set -x
                       """
                        sh "ansible-galaxy install -r ansible/requirements.yml --force --roles-path=ansible/roles"
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook " +
                                " -i ansible/inventory --vault-password-file=\$vault_password_file " +
                                " -e host_env=\$target_environment ansible/kong-service.yml \$limit_hosts"

                    }
                }
            }
            stage ('API Tests') {
                when {
                    expression { params.run_api_tests }
                }
                steps {
                    script {
                        sh "newman run https://api.getpostman.com/collections/\$collection_id?apikey=**************************************************************** --reporters junit,htmlextra --reporter-junit-export postman/junit --reporter-htmlextra-export postman/index.html --environment https://api.getpostman.com/environments/\$postman_env_uid?apikey=****************************************************************"
                    }
                }
                post {
                    always {
                        publishHTML target: [
                            allowMissing: false,
                            alwaysLinkToLastBuild: false,
                            keepAll: true,
                            reportDir: 'postman',
                            reportFiles: 'index.html',
                            reportName: 'Postman Report'
                        ]
                    }
                }
            }

        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                echo 'Pipeline success, sending an email notification...'
                script {
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}
