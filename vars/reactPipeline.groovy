import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils

def call(appName, groupId, appVersion, gitUrl, emailList) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test', 'acc', 'acc2','emr', 'prod'], description: 'Specify the target environment')
            booleanParam(name: 'full_pipeline', defaultValue: true, description: 'When checked, all the pipeline steps will be run ( compile, deploy, ...). If not , only the last deployment step will be run.')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
            string(defaultValue: "", description: 'If you want to limit the deployment to certain hosts of the environment, leave empty to deploy on all nodes.?', name: 'limit_hosts')
        }
        environment {
            def group_id = Utils.parseString(groupId)
            def app_name = Utils.parseString(appName)
            def limit_hosts = Utils.parseLimitHosts("${env.limit_hosts}")
            def app_version = Utils.parseString(appVersion)
            def app_file_name = "${env.app_name}-${env.app_version}.zip"

            def repo_url = Utils.parseNexusUrl(appVersion, "http://repo.degroof.be/nexus/content/repositories/snapshots", "http://repo.degroof.be/nexus/content/repositories/releases")
            def repo_id = "degroofpetercam.repo"

            def email_list = EmailService.parseEmailList(emailList)
            def vault_password_file = "ansible/vault-${env.target_environment}.\$\$.txt"
        }

        stages {
            stage('Deployment notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                    }
                }
            }
            stage ('Load submodules') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'git submodule update --init --recursive'
                    }
                }
            }
            stage ('NPM Install') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'npm install'
                    }
                }
            }
            stage ('Build') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'npm run build --configuration=\$target_environment'
                    }
                }
            }
            stage ('Unit Tests') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'npm run run-tests'
                    }
                }
            }
            stage ('Clean mock') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    dir('public/mock') {
                        deleteDir()
                    }
                }
            }
            stage ('Package') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'cd dist; zip -r ../\$app_file_name *'
                    }
                }
            }
            stage('Push to Nexus') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        sh 'mvn deploy:deploy-file \
                            -Durl=\$repo_url \
                            -DrepositoryId=\$repo_id \
                            -DgroupId=\$group_id \
                            -DartifactId=\$app_name \
                            -Dversion=\$app_version  \
                            -Dpackaging=zip \
                            -Dfile=\$app_file_name'
                    }
                }
            }
            stage ('Deployment') {
                steps {
                    script {
                        sh """
                        set +x
                        echo \$vault_password > \$vault_password_file
                        set -x
                       """
                        sh "ansible-galaxy install -r ansible/requirements.yml --force --roles-path=ansible/roles"
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook -i ansible/inventory --vault-password-file=\$vault_password_file -e host_env=\$target_environment ansible/deploy-static.yml \$limit_hosts"
                    }
                }
            }
        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                script {
                    echo 'Pipeline success, sending an email notification...'
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}