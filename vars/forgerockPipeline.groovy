import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils
import com.degroofpetercam.jenkins.PostmanEnvironment

def call(appName, gitUrl, emailList) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test','test2', 'acc','acc2','emr', 'sandbox', 'prod'], description: 'Specify the target environment')
            booleanParam(name: 'full_pipeline', defaultValue: true, description: 'When checked, all the pipeline steps will be run ( compile, deploy, ...). If not , only the last deployment step will be run.')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
            string(defaultValue: "", description: 'If you want to limit the deployment to certain hosts of the environment, leave empty to deploy on all nodes.?', name: 'limit_hosts')
        }
        environment {
            def vault_password_file = "ansible/vault-${env.target_environment}.\$\$.txt"
            def limit_hosts = Utils.parseLimitHosts("${env.limit_hosts}")
            def email_list = EmailService.parseEmailList(emailList)
            def app_name = Utils.parseString(appName)
            def collection_id = Utils.parseString(collectionId)
        }
        tools {
            jdk 'java11'
        }
        stages {
            stage('Deployment notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                    }
                }
            }
            /*stage ('Release Preparation') {
                steps {
                    script {
                        def pom = readFile('pom.xml')
                        def project = new XmlSlurper().parseText(pom)
                        def v = project.version.text()
                        version = v.replace("-SNAPSHOT", "").trim() + "-" + env.BUILD_NUMBER
                        artifact = project.artifactId.text()
                        group = project.groupId.text()
                        println group
                        println artifact
                        println version
                    }
                }
            }*/
            stage ('Compile') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        configFileProvider([configFile(fileId: '846a73b4-c845-4afb-9c4e-19d582b4051d', variable: 'MAVEN_SETTINGS')]) {
                            sh 'mvn -s $MAVEN_SETTINGS clean compile -DskipTests'
                        }
                    }
                }
            }
            /*stage ('Unit Tests') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        configFileProvider([configFile(fileId: '846a73b4-c845-4afb-9c4e-19d582b4051d', variable: 'MAVEN_SETTINGS')]) {
                            sh 'mvn -s $MAVEN_SETTINGS test -DskipITs'
                        }
                    }
                }
            }*/
            stage('Push to Nexus') {
                when {
                    expression { params.full_pipeline }
                }
                steps {
                    script {
                        configFileProvider([configFile(fileId: '846a73b4-c845-4afb-9c4e-19d582b4051d', variable: 'MAVEN_SETTINGS')]) {
                            sh 'mvn -s $MAVEN_SETTINGS deploy -DskipTests'
                        }
                    }
                }
            }
            stage ('Deployment') {
                steps {
                    script {
                        sh """
                        set +x
                        echo \$vault_password > \$vault_password_file
                        set -x
                       """
                        sh "ansible-galaxy install -r ansible/requirements.yml --force --roles-path=ansible/roles"
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook -i ansible/inventory --vault-password-file=\$vault_password_file -e host_env=\$target_environment ansible/playbook.yml \$limit_hosts"
                    }
                }
            }
        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                echo 'Pipeline success, sending an email notification...'
                script {
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}
