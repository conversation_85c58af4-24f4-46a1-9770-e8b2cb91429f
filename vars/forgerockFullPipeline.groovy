pipeline {
    agent any
    parameters {
        choice(name: 'target_environment', choices: ['test', 'test2','acc','acc2','emr', 'prod'], description: 'Specify the target environment')
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'build_branch', type: 'PT_BRANCH', useRepository: '.*forgerock-repo.git'
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'deploy_branch', type: 'PT_BRANCH', useRepository: '.*provision/forgerock-playbook.git'
        password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
    }
    stages {
        stage('Git Config') {
            steps {
                git branch: "${params.build_branch}", url: '******************:forgerock/???.git'
                git branch: "${params.deploy_branch}", url: '******************:provisioning/forgerock-playbook.git'
            }
        }
        stage('Build forgerock') {
            steps {
                sh "echo building forgerock..."
                build job: 'build-forgerock', parameters: [string(name: 'target_environment', value: "${params.target_environment}"), booleanParam(name: 'full_pipeline', value: "${params.full_pipeline}"), string(name: 'target_branch', value: "${params.fe_branch}"), password(name: 'vault_password', value: "${params.vault_password}")]
            }
        }

        stage('Deploy forgerock') {
            steps {
                sh "echo deploying forgerock..."
                build job: 'deploy-forgerock', parameters: [string(name: 'target_environment', value: "${params.target_environment}"), booleanParam(name: 'full_pipeline', value: "${params.full_pipeline}"), string(name: 'target_branch', value: "${params.experience_branch}"), password(name: 'vault_password', value: "${params.vault_password}")]
            }
        }
    }
    post {
        always {
            echo 'Pipeline finished : delete workspace'
            deleteDir() /* clean up our workspace */
        }
        success {
            echo 'Pipeline success'
        }
    }
}