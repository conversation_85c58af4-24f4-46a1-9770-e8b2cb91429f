import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils

def call(appName, gitUrl, emailList) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test', 'acc', 'prod'], description: 'Specify the target environment')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
            string(defaultValue: "", description: 'If you want to limit the deployment to certain hosts of the environment, leave empty to deploy on all nodes.', name: 'limit_hosts')
            string(defaultValue: "/data/app/kre/working_dir", description: 'Working directory on KRE VM for project execution', name: 'kre_working_dir')
            booleanParam(name: 'cleanup_after_execution', defaultValue: true, description: 'Clean up working directory after test execution')
        }
        environment {
            def vault_password_file = "ansible/vault-${env.target_environment}.\$\$.txt"
            def limit_hosts = Utils.parseLimitHosts("${env.limit_hosts}")
            def email_list = EmailService.parseEmailList(emailList)
            def app_name = Utils.parseString(appName)
            def ansible_password = Utils.defaultPassword("${env.vault_password}")
            def kre_work_dir = Utils.parseString("${env.kre_working_dir}")
        }
        stages {
            stage('Test execution notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                        wrap([$class: 'BuildUser']) {
                            currentBuild.displayName = "$target_environment - KRE Tests"
                            currentBuild.description = "By ${BUILD_USER}"
                        }
                    }
                }
            }

            stage('Create vault password file') {
                steps {
                    script {
                        sh """
                        set +x
                        echo \$ansible_password > \$vault_password_file
                        set -x
                       """
                    }
                }
            }
            stage('Install Ansible Galaxy roles') {
                steps {
                    script {
                        // Install Galaxy roles if requirements.yml has content
                        sh """
                        if [ -s ansible/requirements.yml ] && [ "\$(grep -v '^#' ansible/requirements.yml | grep -v '^\\s*\$' | wc -l)" -gt 0 ]; then
                            echo "Installing Ansible Galaxy roles..."
                            ansible-galaxy install -r ansible/requirements.yml --force --roles-path=ansible/roles
                        else
                            echo "No Ansible Galaxy roles required - skipping installation"
                        fi
                        """
                    }
                }
            }
            stage('Project Transfer to KRE VM') {
                steps {
                    script {
                        echo "Transferring project to KRE VM..."
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook " +
                                "-i ansible/inventory --vault-password-file=\$vault_password_file " +
                                "-e host_env=\$target_environment " +
                                "-e kre_working_directory=\$kre_work_dir " +
                                "-e project_source_path=\$WORKSPACE " +
                                "ansible/kre-project-transfer.yml \$limit_hosts"
                    }
                }
            }
            stage('Execute KRE Tests') {
                steps {
                    script {
                        echo "Executing Katalon Runtime Engine tests..."
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook " +
                                "-i ansible/inventory --vault-password-file=\$vault_password_file " +
                                "-e host_env=\$target_environment " +
                                "-e kre_working_directory=\$kre_work_dir " +
                                "ansible/kre-test-execution.yml \$limit_hosts"
                    }
                }
            }
            stage('Collect Test Results') {
                steps {
                    script {
                        echo "Collecting test results from KRE VM..."
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook " +
                                "-i ansible/inventory --vault-password-file=\$vault_password_file " +
                                "-e host_env=\$target_environment " +
                                "-e kre_working_directory=\$kre_work_dir " +
                                "-e results_destination=\$WORKSPACE/test-results " +
                                "ansible/kre-results-collection.yml \$limit_hosts"
                    }
                }
            }
            stage('Cleanup KRE VM') {
                when {
                    expression { params.cleanup_after_execution }
                }
                steps {
                    script {
                        echo "Cleaning up working directory on KRE VM..."
                        sh "source /data/tmp/jenkins-ansible-debug/ansible/hacking/env-setup;ansible-playbook " +
                                "-i ansible/inventory --vault-password-file=\$vault_password_file " +
                                "-e host_env=\$target_environment " +
                                "-e kre_working_directory=\$kre_work_dir " +
                                "ansible/kre-cleanup.yml \$limit_hosts"
                    }
                }
            }
        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                echo 'KRE Pipeline success, sending an email notification...'
                script {
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'KRE Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}
