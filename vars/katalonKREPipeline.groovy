import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils

def call(appName, gitUrl, emailList) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test', 'acc', 'prod'], description: 'Specify the target environment')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
            string(defaultValue: "", description: 'If you want to limit the deployment to certain hosts of the environment, leave empty to deploy on all nodes.', name: 'limit_hosts')
            string(defaultValue: "/data/app/kre/working_dir", description: 'Working directory on KRE VM for project execution', name: 'kre_working_dir')
            booleanParam(name: 'cleanup_after_execution', defaultValue: true, description: 'Clean up working directory after test execution')
        }
        environment {
            def vault_password_file = "ansible/vault-${env.target_environment}.\$\$.txt"
            def limit_hosts = Utils.parseLimitHosts("${env.limit_hosts}")
            def email_list = EmailService.parseEmailList(emailList)
            def app_name = Utils.parseString(appName)
            def ansible_password = Utils.defaultPassword("${env.vault_password}")
            def kre_work_dir = Utils.parseString("${env.kre_working_dir}")
        }
        stages {
            stage('Test execution notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                        wrap([$class: 'BuildUser']) {
                            currentBuild.displayName = "$target_environment - KRE Tests"
                            currentBuild.description = "By ${BUILD_USER}"
                        }
                    }
                }
            }
            stage('Create vault password file') {
                steps {
                    script {
                        sh """
                        set +x
                        echo \$ansible_password > \$vault_password_file
                        set -x
                       """
                    }
                }
            }
            stage('Install Ansible Galaxy roles') {
                steps {
                    script {
                        sh "ansible-galaxy install -r ansible/requirements.yml --force --roles-path=ansible/roles"
                    }
                }
            }
            stage('Project Transfer to KRE VM') {
                steps {
                    script {
                        echo "Transferring project to KRE VM using rsync..."
                        // Create unique project directory name with timestamp
                        def projectDir = "${env.BUILD_NUMBER}-${env.JOB_NAME.replaceAll('/', '-')}-katalon"
                        def remoteWorkDir = "${env.kre_working_dir}/${projectDir}"

                        // Get KRE VM hostname from ansible inventory (assuming it's defined there)
                        def kreHost = sh(
                            script: "grep -A 10 '\\[kre_servers\\]' ansible/inventory | grep -v '\\[' | head -1 | awk '{print \$1}'",
                            returnStdout: true
                        ).trim()

                        // Create working directory on KRE VM
                        sh "ssh ansible@${kreHost} 'sudo mkdir -p ${remoteWorkDir} && sudo chown katalon01:katalon01 ${remoteWorkDir}'"

                        // Rsync project files to KRE VM
                        sh "rsync -avz --delete --exclude='.git' --exclude='target' --exclude='node_modules' \$WORKSPACE/ ansible@${kreHost}:${remoteWorkDir}/"

                        // Fix ownership after rsync
                        sh "ssh ansible@${kreHost} 'sudo chown -R katalon01:katalon01 ${remoteWorkDir}'"

                        // Store the remote project path for subsequent stages
                        env.REMOTE_PROJECT_PATH = remoteWorkDir
                        env.KRE_HOST = kreHost

                        echo "Project transferred to ${kreHost}:${remoteWorkDir}"
                    }
                }
            }
            stage('Execute KRE Tests') {
                steps {
                    script {
                        echo "Executing Katalon Runtime Engine tests..."

                        // Execute KRE CLI command on remote VM as katalon01 user
                        def kreCommand = """
                            cd ${env.REMOTE_PROJECT_PATH} && \\
                            echo '=== Katalon Runtime Engine Test Execution ===' && \\
                            echo 'Project Path: ${env.REMOTE_PROJECT_PATH}' && \\
                            echo 'Environment: ${env.target_environment}' && \\
                            echo 'Timestamp: \$(date)' && \\
                            echo 'User: \$(whoami)' && \\
                            echo 'Working Directory: \$(pwd)' && \\
                            echo '=== This is a placeholder for actual KRE CLI command ===' && \\
                            echo 'Command would be: katalonc -noSplash -runMode=console -projectPath=. -retry=0 -testSuitePath=\"Test Suites/YourTestSuite\" -executionProfile=default -browserType=\"Chrome (headless)\"' && \\
                            echo '=== Test execution completed ===' && \\
                            mkdir -p Reports && \\
                            echo 'Mock test results generated' > Reports/test-results.log
                        """

                        sh "ssh ansible@${env.KRE_HOST} 'sudo -u katalon01 bash -c \"${kreCommand}\"'"

                        echo "KRE test execution completed"
                    }
                }
            }
            stage('Collect Test Results') {
                steps {
                    script {
                        echo "Collecting test results from KRE VM..."

                        // Create local results directory
                        sh "mkdir -p \$WORKSPACE/test-results"

                        // Copy test results back from KRE VM using rsync
                        sh "rsync -avz ansible@${env.KRE_HOST}:${env.REMOTE_PROJECT_PATH}/Reports/ \$WORKSPACE/test-results/ || echo 'No results directory found'"

                        // Generate summary
                        sh """
                            echo '=== Katalon Test Results Summary ===' > \$WORKSPACE/test-results/summary.txt
                            echo 'Collection Date: \$(date)' >> \$WORKSPACE/test-results/summary.txt
                            echo 'Project Path: ${env.REMOTE_PROJECT_PATH}' >> \$WORKSPACE/test-results/summary.txt
                            echo 'Environment: ${env.target_environment}' >> \$WORKSPACE/test-results/summary.txt
                            echo 'KRE Host: ${env.KRE_HOST}' >> \$WORKSPACE/test-results/summary.txt
                            if [ -f '\$WORKSPACE/test-results/test-results.log' ]; then
                                echo '=== Test Results ===' >> \$WORKSPACE/test-results/summary.txt
                                cat '\$WORKSPACE/test-results/test-results.log' >> \$WORKSPACE/test-results/summary.txt
                            fi
                        """

                        echo "Test results collected to \$WORKSPACE/test-results"
                        echo "In a real implementation, this would process JUnit XML files, HTML reports, and other Katalon-generated artifacts"
                    }
                }
            }
            stage('Cleanup KRE VM') {
                when {
                    expression { params.cleanup_after_execution }
                }
                steps {
                    script {
                        echo "Cleaning up working directory on KRE VM..."

                        // Remove the project directory from KRE VM
                        sh "ssh ansible@${env.KRE_HOST} 'sudo rm -rf ${env.REMOTE_PROJECT_PATH}'"

                        echo "Cleanup completed: removed ${env.REMOTE_PROJECT_PATH} from ${env.KRE_HOST}"
                    }
                }
            }
        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                echo 'KRE Pipeline success, sending an email notification...'
                script {
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'KRE Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}
