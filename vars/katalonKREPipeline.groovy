import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils

def call(appName, gitUrl, emailList) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test', 'acc', 'prod'], description: 'Specify the target environment')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            string(defaultValue: "kre-vm-hostname", description: 'KRE VM hostname or IP address', name: 'kre_host')
            string(defaultValue: "/data/app/kre/working_dir", description: 'Working directory on KRE VM for project execution', name: 'kre_working_dir')
            choice(name: 'ssh_auth_method', choices: ['ssh_key', 'password'], description: 'SSH authentication method')
            password(name: 'ssh_password', defaultValue: 'SECRET', description: 'SSH password for ansible user (only if password auth is selected)')
            booleanParam(name: 'cleanup_after_execution', defaultValue: true, description: 'Clean up working directory after test execution')
        }
        environment {
            def email_list = EmailService.parseEmailList(emailList)
            def app_name = Utils.parseString(appName)
            def kre_work_dir = Utils.parseString("${env.kre_working_dir}")
            def kre_hostname = Utils.parseString("${env.kre_host}")
            def ssh_auth = Utils.parseString("${env.ssh_auth_method}")
            def ssh_pass = Utils.defaultPassword("${env.ssh_password}")
        }
        stages {
            stage('Test execution notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                        wrap([$class: 'BuildUser']) {
                            currentBuild.displayName = "$target_environment - KRE Tests"
                            currentBuild.description = "By ${BUILD_USER}"
                        }
                    }
                }
            }

            stage('Project Transfer to KRE VM') {
                steps {
                    script {
                        echo "Transferring project to KRE VM using rsync..."
                        // Create unique project directory name with timestamp
                        def projectDir = "${env.BUILD_NUMBER}-${env.JOB_NAME.replaceAll('/', '-')}-katalon"
                        def remoteWorkDir = "${env.kre_working_dir}/${projectDir}"

                        // Use the KRE host from parameters
                        def kreHost = env.kre_hostname

                        // Prepare SSH/rsync commands based on authentication method
                        def sshCmd = ""
                        def rsyncCmd = ""

                        if (env.ssh_auth == "password") {
                            // Use sshpass for password authentication
                            sshCmd = "sshpass -p '\$ssh_pass' ssh -o StrictHostKeyChecking=no ansible@${kreHost}"
                            rsyncCmd = "sshpass -p '\$ssh_pass' rsync -avz --delete --exclude='.git' --exclude='target' --exclude='node_modules' -e 'ssh -o StrictHostKeyChecking=no' \$WORKSPACE/ ansible@${kreHost}:${remoteWorkDir}/"
                        } else {
                            // Use SSH key authentication (default)
                            sshCmd = "ssh -o StrictHostKeyChecking=no ansible@${kreHost}"
                            rsyncCmd = "rsync -avz --delete --exclude='.git' --exclude='target' --exclude='node_modules' \$WORKSPACE/ ansible@${kreHost}:${remoteWorkDir}/"
                        }

                        // Create working directory on KRE VM
                        sh "${sshCmd} 'sudo mkdir -p ${remoteWorkDir} && sudo chown katalon01:katalon01 ${remoteWorkDir}'"

                        // Rsync project files to KRE VM
                        sh rsyncCmd

                        // Fix ownership after rsync
                        sh "${sshCmd} 'sudo chown -R katalon01:katalon01 ${remoteWorkDir}'"

                        // Store the remote project path for subsequent stages
                        env.REMOTE_PROJECT_PATH = remoteWorkDir
                        env.KRE_HOST = kreHost

                        echo "Project transferred to ${kreHost}:${remoteWorkDir}"
                    }
                }
            }
            stage('Execute KRE Tests') {
                steps {
                    script {
                        echo "Executing Katalon Runtime Engine tests..."

                        // Execute KRE CLI command on remote VM as katalon01 user
                        def kreCommand = """
                            cd ${env.REMOTE_PROJECT_PATH} && \\
                            echo '=== Katalon Runtime Engine Test Execution ===' && \\
                            echo 'Project Path: ${env.REMOTE_PROJECT_PATH}' && \\
                            echo 'Environment: ${env.target_environment}' && \\
                            echo 'Timestamp: \$(date)' && \\
                            echo 'User: \$(whoami)' && \\
                            echo 'Working Directory: \$(pwd)' && \\
                            echo '=== This is a placeholder for actual KRE CLI command ===' && \\
                            echo 'Command would be: katalonc -noSplash -runMode=console -projectPath=. -retry=0 -testSuitePath=\"Test Suites/YourTestSuite\" -executionProfile=default -browserType=\"Chrome (headless)\"' && \\
                            echo '=== Test execution completed ===' && \\
                            mkdir -p Reports && \\
                            echo 'Mock test results generated' > Reports/test-results.log
                        """

                        // Use appropriate SSH command based on auth method
                        def sshExecCmd = ""
                        if (env.ssh_auth == "password") {
                            sshExecCmd = "sshpass -p '\$ssh_pass' ssh -o StrictHostKeyChecking=no ansible@${env.KRE_HOST}"
                        } else {
                            sshExecCmd = "ssh -o StrictHostKeyChecking=no ansible@${env.KRE_HOST}"
                        }

                        sh "${sshExecCmd} 'sudo -u katalon01 bash -c \"${kreCommand}\"'"

                        echo "KRE test execution completed"
                    }
                }
            }
            stage('Collect Test Results') {
                steps {
                    script {
                        echo "Collecting test results from KRE VM..."

                        // Create local results directory
                        sh "mkdir -p \$WORKSPACE/test-results"

                        // Copy test results back from KRE VM using rsync
                        def rsyncBackCmd = ""
                        if (env.ssh_auth == "password") {
                            rsyncBackCmd = "sshpass -p '\$ssh_pass' rsync -avz -e 'ssh -o StrictHostKeyChecking=no' ansible@${env.KRE_HOST}:${env.REMOTE_PROJECT_PATH}/Reports/ \$WORKSPACE/test-results/ || echo 'No results directory found'"
                        } else {
                            rsyncBackCmd = "rsync -avz ansible@${env.KRE_HOST}:${env.REMOTE_PROJECT_PATH}/Reports/ \$WORKSPACE/test-results/ || echo 'No results directory found'"
                        }
                        sh rsyncBackCmd

                        // Generate summary
                        sh """
                            echo '=== Katalon Test Results Summary ===' > \$WORKSPACE/test-results/summary.txt
                            echo 'Collection Date: \$(date)' >> \$WORKSPACE/test-results/summary.txt
                            echo 'Project Path: ${env.REMOTE_PROJECT_PATH}' >> \$WORKSPACE/test-results/summary.txt
                            echo 'Environment: ${env.target_environment}' >> \$WORKSPACE/test-results/summary.txt
                            echo 'KRE Host: ${env.KRE_HOST}' >> \$WORKSPACE/test-results/summary.txt
                            if [ -f '\$WORKSPACE/test-results/test-results.log' ]; then
                                echo '=== Test Results ===' >> \$WORKSPACE/test-results/summary.txt
                                cat '\$WORKSPACE/test-results/test-results.log' >> \$WORKSPACE/test-results/summary.txt
                            fi
                        """

                        echo "Test results collected to \$WORKSPACE/test-results"
                        echo "In a real implementation, this would process JUnit XML files, HTML reports, and other Katalon-generated artifacts"
                    }
                }
            }
            stage('Cleanup KRE VM') {
                when {
                    expression { params.cleanup_after_execution }
                }
                steps {
                    script {
                        echo "Cleaning up working directory on KRE VM..."

                        // Remove the project directory from KRE VM
                        sh "ssh ansible@${env.KRE_HOST} 'sudo rm -rf ${env.REMOTE_PROJECT_PATH}'"

                        echo "Cleanup completed: removed ${env.REMOTE_PROJECT_PATH} from ${env.KRE_HOST}"
                    }
                }
            }
        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                echo 'KRE Pipeline success, sending an email notification...'
                script {
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'KRE Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}
