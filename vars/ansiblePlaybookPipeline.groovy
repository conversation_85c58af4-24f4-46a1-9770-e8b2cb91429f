import com.degroofpetercam.jenkins.EmailService
import com.degroofpetercam.jenkins.Utils

def call(appName, gitUrl, emailList) {
    pipeline {
        agent any
        parameters {
            choice(name: 'target_environment', choices: ['test','test-be','test-lu', 'acc', 'acc2','acc-be','acc-lu', 'sandbox', 'emr','emr-be','emr-lu','prod','prod-be','prod-lu'], description: 'Specify the target environment')
            gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: 'target_branch', type: 'PT_BRANCH', useRepository: gitUrl
            password(name: 'vault_password', defaultValue: 'SECRET', description: 'The password of the vault file of the selected environment.')
            string(defaultValue: "", description: 'If you want to limit the deployment to certain hosts of the environment, leave empty to deploy on all nodes.?', name: 'limit_hosts')
        }
        environment {
            def vault_password_file = "vault-${env.target_environment}.\$\$.txt"
            def limit_hosts = Utils.parseLimitHosts("${env.limit_hosts}")
            def email_list = EmailService.parseEmailList(emailList)
            def app_name = Utils.parseString(appName)
        }
        stages {
            stage('Deployment notification') {
                steps {
                    script {
                        EmailService.startEmail(this)
                    }
                }
            }
            stage ('Create password file') {
                steps {
                    script {
                        sh """
                        set +x
                        echo \$vault_password > \$vault_password_file
                        set -x
                       """
                    }
                }
            }
            stage ('Install Ansible Galaxy roles') {
                steps {
                    script {
                        sh "ansible-galaxy install -r requirements.yml --force --roles-path=./roles"
                    }
                }
            }
            stage ('Run Ansible playbook') {
                steps {
                    script {
                        sh "ansible-playbook -i inventory --vault-password-file=\$vault_password_file -e host_env=\$target_environment playbook.yml \$limit_hosts"
                    }
                }
            }
        }
        post {
            always {
                echo 'Pipeline finished : delete workspace'
                deleteDir() /* clean up our workspace */
            }
            success {
                echo 'Pipeline success, sending an email notification...'
                script {
                    EmailService.successEmail(this)
                }
            }
            failure {
                echo 'Pipeline failure, sending an email notification...'
                script {
                    EmailService.failureEmail(this)
                }
            }
        }
    }
}
