Katalon Runtime Engine (KRE) Pipeline

This pipeline executes Katalon Studio test projects using the Katalon Runtime Engine on a remote VM via Ansible.

USAGE:
katalonKREPipeline(appName, gitUrl, emailList)

PARAMETERS:
- appName: String - Name of the Katalon test project
- gitUrl: String - Git repository URL containing the Katalon project
- emailList: String - Comma-separated list of email addresses for notifications

PIPELINE PARAMETERS:
- target_environment: Choice - Target environment (test, acc, prod)
- target_branch: Git Parameter - Branch to checkout and test
- vault_password: Password - Ansible vault password for the selected environment
- limit_hosts: String - Limit execution to specific hosts (optional)
- kre_working_dir: String - Working directory on KRE VM (default: /data/app/kre/working_dir)
- cleanup_after_execution: Boolean - Clean up working directory after execution (default: true)

PIPELINE STAGES:
1. Test execution notification - Sends start notification email
2. Create vault password file - Creates Ansible vault password file
3. Install Ansible Galaxy roles - Installs required Ansible roles
4. Project Transfer to KRE VM - Uses rsync to transfer project files to KRE VM
5. Execute KRE Tests - Runs Katalon tests using KRE CLI via SSH
6. Collect Test Results - Retrieves test results and logs using rsync
7. Cleanup KRE VM - Removes working directory via SSH (optional)

TECHNICAL APPROACH:
- Uses rsync for efficient file transfer instead of complex Ansible playbooks
- Direct SSH commands for test execution and cleanup
- Minimal Ansible dependency (only for inventory management)
- Creates unique working directories per build to avoid conflicts

INFRASTRUCTURE REQUIREMENTS:
- Jenkins VM with local agent
- KRE VM accessible via SSH with ansible user
- katalon01 user on KRE VM for test execution
- Ansible inventory configured with KRE VM details (ansible/inventory with [kre_servers] section)
- SSH key-based authentication between Jenkins and KRE VM
- rsync installed on both Jenkins and KRE VMs

EXAMPLE USAGE:
katalonKREPipeline('my-katalon-tests', 'https://git.company.com/katalon/my-tests.git', '<EMAIL>')
