package com.degroofpetercam.jenkins
class Utils {
    def static parseString(String s) {
        return "" + s
    }

    def static parseLimitHosts(String limitHosts) {
        if (limitHosts?.trim()) {
            return "--limit " + limitHosts
        } else {
            return ""
        }
    }

    def static parseNexusUrl(appVersion, snapshotsUrl, releasesUrl) {
        return appVersion.contains("SNAPSHOT") ? snapshotsUrl : releasesUrl
    }

    def static defaultPassword(String param) {
        return param ? param : "HjIuuyZP1Yv8bsd2sGXk"
    }
}