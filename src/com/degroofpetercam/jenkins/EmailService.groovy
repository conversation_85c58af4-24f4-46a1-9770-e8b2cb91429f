package com.degroofpetercam.jenkins
class EmailService {
    def static parseEmailList(emailList) {
        String email_list = ""
        for (email in emailList) {
            email_list += email
            if (email != emailList.last())
                email_list += ","
        }
        return email_list
    }

    def static startEmail(script) {
        script.echo "Sending mail to: ${script.env.email_list}"
        script.emailext (
                body: startEmailText(script, buildChangeLog(script.currentBuild.changeSets)),
                recipientProviders: [script.requestor()],
                subject: "New deployment of ${script.env.app_name} to [${script.env.target_environment}] environment",
                to: "${script.env.email_list}"
        )

    }

    def static successEmail(script) {
        script.echo "Sending mail to: ${script.env.email_list}"
        script.emailext (
                body: successEmailText(script, buildChangeLog(script.currentBuild.changeSets)),
                recipientProviders: [script.requestor()],
                subject: "${script.env.app_name} deployment to [${script.env.target_environment}] has successfully completed.",
                to: "${script.env.email_list}"
        )
    }

    def static failureEmail(script) {
        script.echo "Sending mail to: ${script.env.email_list}"
        script.emailext (
                body: failureEmailText(script, buildChangeLog(script.currentBuild.changeSets)),
                recipientProviders: [script.requestor()],
                subject: "${script.env.app_name} deployment to [${script.env.target_environment}] has failed.",
                to: "${script.env.email_list}"
        )
    }

    def static startEmailText(script, changelog) {
        return """\
            Hi,
            
            A new job [${script.env.JOB_NAME}] is started :
                - Environment : ${script.env.target_environment}
                - Console : ${script.env.BUILD_URL}
                - Changelog : ${changelog}

            Please be advised that the ${script.env.app_name} in ${script.env.target_environment} will be unavailable until the end of the pipeline execution.
            We will keep you informed as soon as the deployment ends.
            
            Best regards,
            Jenkins on behalf of the ${script.env.app_name} team.
        """.stripIndent()
    }

    def static successEmailText(script, changelog) {
        return """\
            Hi,
            
            The job [${script.env.JOB_NAME}] was successfully run :
                - Environment : ${script.env.target_environment}
                - Duration : ${script.currentBuild.duration} [milliseconds].
                - Console : ${script.env.BUILD_URL}
                - Changelog : ${changelog}

            The ${script.env.app_name} ${script.env.target_environment} environment should be available now.
            Thank you for your patience.
            
            Best regards,
            Jenkins on behalf of the ${script.env.app_name} team.
        """.stripIndent()
    }

    def static failureEmailText(script, changelog) {
        return """\
            Hi,
            
            The job [${script.env.JOB_NAME}] has failed :
                - Environment : ${script.env.target_environment}
                - Duration : ${script.currentBuild.duration} [milliseconds].
                - Console : ${script.env.BUILD_URL}
                - Changelog : ${changelog}
            
            Best regards,
            Jenkins on behalf of the ${script.env.app_name} team.
        """.stripIndent()
    }

    def static buildChangeLog(changeLogSets) {
        def sb = new StringBuilder()
        for (int i = 0; i < changeLogSets.size(); i++) {
            def entries = changeLogSets[i].items
            for (int j = 0; j < entries.length; j++) {
                def entry = entries[j]
                sb.append("${entry.commitId} by ${entry.author} on ${new Date(entry.timestamp)}: ${entry.msg}")
                def files = new ArrayList(entry.affectedFiles)
                for (int k = 0; k < files.size(); k++) {
                    def file = files[k]
                    sb.append("  ${file.editType.name} ${file.path}")
                }
                sb.append("\n")
            }
        }
        return sb.toString()
    }
}