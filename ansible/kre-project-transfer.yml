---
- name: Transfer Katalon project to KRE VM
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon01"
    project_name: "{{ ansible_date_time.epoch }}-katalon-project"
    full_working_path: "{{ kre_working_directory }}/{{ project_name }}"
    
  tasks:
    - name: Ensure KRE working directory exists
      file:
        path: "{{ kre_working_directory }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'

    - name: Create project-specific working directory
      file:
        path: "{{ full_working_path }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'

    - name: Sync project files to KRE VM using rsync
      synchronize:
        src: "{{ project_source_path }}/"
        dest: "{{ full_working_path }}/"
        delete: yes
        rsync_opts:
          - "--exclude=.git"
          - "--exclude=target"
          - "--exclude=node_modules"
          - "--exclude=.idea"

    - name: Set correct ownership for project files
      file:
        path: "{{ full_working_path }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'
        recurse: yes

    - name: Store project path in a fact file for subsequent playbooks
      copy:
        content: "{{ full_working_path }}"
        dest: "/tmp/katalon_project_path.txt"
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0644'

    - name: Display project transfer completion
      debug:
        msg: "Project successfully transferred to {{ full_working_path }}"
