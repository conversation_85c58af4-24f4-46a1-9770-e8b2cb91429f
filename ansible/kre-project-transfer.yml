---
- name: Transfer Katalon project to KRE VM
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon01"
    ansible_user: "ansible"
    project_name: "{{ ansible_date_time.epoch }}-katalon-project"
    full_working_path: "{{ kre_working_directory }}/{{ project_name }}"
    
  tasks:
    - name: Ensure KRE working directory exists
      file:
        path: "{{ kre_working_directory }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'
      become_user: root

    - name: Create project-specific working directory
      file:
        path: "{{ full_working_path }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'
      become_user: root

    - name: Create temporary archive of project files
      archive:
        path: "{{ project_source_path }}"
        dest: "/tmp/katalon-project-{{ ansible_date_time.epoch }}.tar.gz"
        format: gz
      delegate_to: localhost
      become: no

    - name: Transfer project archive to KRE VM
      copy:
        src: "/tmp/katalon-project-{{ ansible_date_time.epoch }}.tar.gz"
        dest: "/tmp/katalon-project-{{ ansible_date_time.epoch }}.tar.gz"
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0644'

    - name: Extract project files to working directory
      unarchive:
        src: "/tmp/katalon-project-{{ ansible_date_time.epoch }}.tar.gz"
        dest: "{{ full_working_path }}"
        remote_src: yes
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        extra_opts: [--strip-components=1]

    - name: Remove temporary archive from KRE VM
      file:
        path: "/tmp/katalon-project-{{ ansible_date_time.epoch }}.tar.gz"
        state: absent

    - name: Remove temporary archive from Jenkins
      file:
        path: "/tmp/katalon-project-{{ ansible_date_time.epoch }}.tar.gz"
        state: absent
      delegate_to: localhost
      become: no

    - name: Set project directory permissions
      file:
        path: "{{ full_working_path }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'
        recurse: yes

    - name: Store project path for subsequent playbooks
      set_fact:
        katalon_project_path: "{{ full_working_path }}"

    - name: Display project transfer completion
      debug:
        msg: "Project successfully transferred to {{ full_working_path }}"
