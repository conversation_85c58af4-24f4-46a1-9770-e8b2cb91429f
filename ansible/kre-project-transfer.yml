---
- name: Transfer Katalon project to KRE VM
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon01"
    project_name: "{{ ansible_date_time.epoch }}-katalon-project"
    full_working_path: "{{ kre_working_directory }}/{{ project_name }}"
    
  tasks:
    - name: Ensure KRE working directory exists
      file:
        path: "{{ kre_working_directory }}"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'

    - name: Create project-specific working directory as katalon01 user
      file:
        path: "{{ full_working_path }}"
        state: directory
        mode: '0755'
      become_user: "{{ kre_user }}"

    - name: Sync project files using direct rsync as katalon01 user
      shell: |
        rsync -avz --delete \
          --exclude='.git' \
          --exclude='target' \
          --exclude='node_modules' \
          --exclude='.idea' \
          "{{ project_source_path }}/" "{{ full_working_path }}/"
      become_user: "{{ kre_user }}"

    - name: Store project path in a fact file for subsequent playbooks
      copy:
        content: "{{ full_working_path }}"
        dest: "/tmp/katalon_project_path.txt"
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0644'

    - name: Display project transfer completion
      debug:
        msg: "Project successfully transferred to {{ full_working_path }}"
