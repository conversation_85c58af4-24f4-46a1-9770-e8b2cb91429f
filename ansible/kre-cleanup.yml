---
- name: Cleanup Katalon project from KRE VM
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon101p"
    
  tasks:
    - name: Cleanup project directory and old directories as katalon101p user
      shell: |
        # Read and remove current project
        if [ -f "/tmp/katalon_project_path.txt" ]; then
          PROJECT_PATH=$(cat /tmp/katalon_project_path.txt)
          if [ -n "$PROJECT_PATH" ] && [ -d "$PROJECT_PATH" ]; then
            echo "Removing current project directory: $PROJECT_PATH"
            rm -rf "$PROJECT_PATH"
          fi
          rm -f /tmp/katalon_project_path.txt
        fi

        # Clean up old project directories (older than 1 day)
        OLD_COUNT=0
        if [ -d "{{ kre_working_directory }}" ]; then
          for dir in $(find "{{ kre_working_directory }}" -maxdepth 1 -name "*-katalon-project" -type d -mtime +1 2>/dev/null); do
            echo "Removing old project directory: $dir"
            rm -rf "$dir"
            OLD_COUNT=$((OLD_COUNT + 1))
          done
        fi

        echo "Cleanup completed. Removed current project and $OLD_COUNT old directories"
      register: cleanup_result
      become_user: "{{ kre_user }}"

    - name: Display cleanup completion
      debug:
        msg: "{{ cleanup_result.stdout_lines }}"
