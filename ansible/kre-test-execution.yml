---
- name: Execute Katalon Runtime Engine tests
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon01"
    
  tasks:
    - name: Read project path from fact file as katalon01 user
      shell: cat /tmp/katalon_project_path.txt
      register: project_path_result
      become_user: "{{ kre_user }}"

    - name: Set project path variable
      set_fact:
        katalon_project_path: "{{ project_path_result.stdout | trim }}"

    - name: Display project path
      debug:
        msg: "Executing tests in project: {{ katalon_project_path }}"

    - name: Check project directory and list contents as katalon01 user
      shell: |
        if [ ! -d "{{ katalon_project_path }}" ]; then
          echo "ERROR: Project directory {{ katalon_project_path }} not found"
          exit 1
        fi
        echo "Project directory exists: {{ katalon_project_path }}"
        ls -la "{{ katalon_project_path }}"
      register: project_check_result
      become_user: "{{ kre_user }}"

    - name: Display project contents
      debug:
        msg: "{{ project_check_result.stdout_lines }}"

    - name: Execute KRE CLI command and generate results as katalon01 user
      shell: |
        cd "{{ katalon_project_path }}"
        echo "=== Katalon Runtime Engine Test Execution ==="
        echo "Project Path: {{ katalon_project_path }}"
        echo "Environment: {{ host_env }}"
        echo "Timestamp: $(date)"
        echo "User: $(whoami)"
        echo "Working Directory: $(pwd)"
        echo "=== This is a placeholder for actual KRE CLI command ==="
        echo "Command would be: katalonc -noSplash -runMode=console -projectPath=. -retry=0 -testSuitePath='Test Suites/YourTestSuite' -executionProfile=default -browserType='Chrome (headless)'"
        echo "=== Test execution completed ==="

        # Create Reports directory and generate mock results
        mkdir -p "{{ katalon_project_path }}/Reports"
        cd "{{ katalon_project_path }}/Reports"
        echo "=== Mock Test Results ===" > test-results.log
        echo "Test Suite: Sample Test Suite" >> test-results.log
        echo "Execution Date: $(date)" >> test-results.log
        echo "Environment: {{ host_env }}" >> test-results.log
        echo "Status: PASSED" >> test-results.log
        echo "Total Tests: 5" >> test-results.log
        echo "Passed: 5" >> test-results.log
        echo "Failed: 0" >> test-results.log
        echo "Skipped: 0" >> test-results.log
      register: kre_execution_result
      become_user: "{{ kre_user }}"

    - name: Display KRE execution output
      debug:
        msg: "{{ kre_execution_result.stdout_lines }}"

    - name: Display test execution completion
      debug:
        msg: "KRE test execution completed successfully"
