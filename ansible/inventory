[kre_servers]
svlkatalkre101p ansible_host=svlkatalkre101p ansible_user=ansible ansible_ssh_private_key_file=~/.ssh/id_rsa

[kre_servers:vars]
# Common variables for all KRE servers
ansible_python_interpreter=/usr/bin/python3
ansible_ssh_common_args='-o StrictHostKeyChecking=no'

# Environment-specific groups
[test]
svlkatalkre101p

[acc]
# Add acceptance environment KRE servers here when available

[prod]
# Add production environment KRE servers here when available
