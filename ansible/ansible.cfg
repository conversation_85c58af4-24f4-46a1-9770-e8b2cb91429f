[defaults]
# Basic configuration
inventory = inventory
host_key_checking = False
timeout = 30
retry_files_enabled = False
gathering = smart
fact_caching = memory

# Output formatting
stdout_callback = yaml
bin_ansible_callbacks = True

# SSH settings
[ssh_connection]
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o StrictHostKeyChecking=no
pipelining = True
control_path = /tmp/ansible-ssh-%%h-%%p-%%r

# Privilege escalation
[privilege_escalation]
become = True
become_method = sudo
become_user = root
become_ask_pass = False
