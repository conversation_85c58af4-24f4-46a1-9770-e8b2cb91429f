---
- name: Collect Katalon test results from KRE VM
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon01"
    
  tasks:
    - name: Read project path from fact file
      slurp:
        src: "/tmp/katalon_project_path.txt"
      register: project_path_file

    - name: Set project path variable
      set_fact:
        katalon_project_path: "{{ project_path_file.content | b64decode | trim }}"

    - name: Check if Reports directory exists
      stat:
        path: "{{ katalon_project_path }}/Reports"
      register: reports_dir

    - name: Create Reports directory if it doesn't exist
      file:
        path: "{{ katalon_project_path }}/Reports"
        state: directory
        owner: "{{ kre_user }}"
        group: "{{ kre_user }}"
        mode: '0755'
      when: not reports_dir.stat.exists

    - name: Find all test result files
      find:
        paths: "{{ katalon_project_path }}/Reports"
        patterns: "*.log,*.xml,*.html,*.json"
        recurse: yes
      register: result_files

    - name: Display found result files
      debug:
        msg: "Found {{ result_files.files | length }} result files"

    - name: Create local results directory on <PERSON>
      file:
        path: "{{ results_destination }}"
        state: directory
        mode: '0755'
      delegate_to: localhost
      become: no

    - name: Copy test result files to Jenkins
      fetch:
        src: "{{ item.path }}"
        dest: "{{ results_destination }}/"
        flat: yes
      with_items: "{{ result_files.files }}"
      when: result_files.files | length > 0

    - name: Generate results summary
      shell: |
        echo "=== Katalon Test Results Summary ===" > "{{ katalon_project_path }}/Reports/summary.txt"
        echo "Collection Date: $(date)" >> "{{ katalon_project_path }}/Reports/summary.txt"
        echo "Project Path: {{ katalon_project_path }}" >> "{{ katalon_project_path }}/Reports/summary.txt"
        echo "Environment: {{ host_env }}" >> "{{ katalon_project_path }}/Reports/summary.txt"
        echo "Total Result Files: {{ result_files.files | length }}" >> "{{ katalon_project_path }}/Reports/summary.txt"
        if [ -f "{{ katalon_project_path }}/Reports/test-results.log" ]; then
          echo "=== Test Results ===" >> "{{ katalon_project_path }}/Reports/summary.txt"
          cat "{{ katalon_project_path }}/Reports/test-results.log" >> "{{ katalon_project_path }}/Reports/summary.txt"
        fi

    - name: Copy summary to Jenkins
      fetch:
        src: "{{ katalon_project_path }}/Reports/summary.txt"
        dest: "{{ results_destination }}/summary.txt"
        flat: yes

    - name: Display collection completion message
      debug:
        msg: "Test results collected to {{ results_destination }}"

    - name: Echo placeholder for actual results processing
      debug:
        msg: "In a real implementation, this would process JUnit XML files, HTML reports, and other Katalon-generated artifacts"
