---
- name: Collect Katalon test results from KRE VM
  hosts: kre_servers
  become: yes
  vars:
    kre_user: "katalon01"
    
  tasks:
    - name: Read project path from fact file
      shell: cat /tmp/katalon_project_path.txt
      register: project_path_result
      become_user: "{{ kre_user }}"

    - name: Set project path variable
      set_fact:
        katalon_project_path: "{{ project_path_result.stdout | trim }}"

    - name: Ensure Reports directory exists and find result files as katalon01 user
      shell: |
        mkdir -p "{{ katalon_project_path }}/Reports"
        cd "{{ katalon_project_path }}/Reports"
        find . -name "*.log" -o -name "*.xml" -o -name "*.html" -o -name "*.json" | wc -l
        find . -name "*.log" -o -name "*.xml" -o -name "*.html" -o -name "*.json"
      register: result_files_check
      become_user: "{{ kre_user }}"

    - name: Display found result files
      debug:
        msg: "Found {{ result_files_check.stdout_lines[0] }} result files: {{ result_files_check.stdout_lines[1:] }}"

    - name: Create local results directory on Jenkins
      shell: mkdir -p "{{ results_destination }}"
      delegate_to: localhost
      become: no

    - name: Generate results summary as katalon01 user
      shell: |
        cd "{{ katalon_project_path }}/Reports"
        echo "=== Katalon Test Results Summary ===" > summary.txt
        echo "Collection Date: $(date)" >> summary.txt
        echo "Project Path: {{ katalon_project_path }}" >> summary.txt
        echo "Environment: {{ host_env }}" >> summary.txt
        echo "Total Result Files: $(find . -name "*.log" -o -name "*.xml" -o -name "*.html" -o -name "*.json" | wc -l)" >> summary.txt
        if [ -f "test-results.log" ]; then
          echo "=== Test Results ===" >> summary.txt
          cat "test-results.log" >> summary.txt
        fi
      become_user: "{{ kre_user }}"

    - name: Copy all result files to Jenkins using rsync
      shell: |
        rsync -avz "{{ katalon_project_path }}/Reports/" "{{ results_destination }}/" || echo "No results to copy"
      become_user: "{{ kre_user }}"

    - name: Display collection completion message
      debug:
        msg: "Test results collected to {{ results_destination }}"

    - name: Echo placeholder for actual results processing
      debug:
        msg: "In a real implementation, this would process JUnit XML files, HTML reports, and other Katalon-generated artifacts"
