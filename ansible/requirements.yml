# Ansible Galaxy Requirements for Katalon KRE Pipeline
# Currently no external roles are required for basic KRE testing
# Add roles here as needed for future enhancements

# Example of how to add roles when needed:
# - name: geerlingguy.java
#   version: "1.10.0"
# - name: community.general
#   version: ">=3.0.0"

# For now, this file can remain empty but is required by the pipeline
collections: []
roles: []
