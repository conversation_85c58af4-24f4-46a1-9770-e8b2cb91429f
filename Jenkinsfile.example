// Alternative Jenkinsfile examples for different use cases
// No @Library needed since katalonKRE<PERSON>ipeline is in vars/ folder

// Example 1: Basic usage (same as main Jenkinsfile)
pipeline {
    agent any
    
    stages {
        stage('Run Katalon Tests') {
            steps {
                script {
                    katalonKREPipeline(
                        'my-katalon-project',
                        'https://github.com/your-org/katalon-project.git',
                        '<EMAIL>,<EMAIL>'
                    )
                }
            }
        }
    }
}

// Example 2: With additional stages
/*
pipeline {
    agent any
    
    stages {
        stage('Pre-Test Setup') {
            steps {
                echo 'Setting up test environment...'
                // Add any pre-test setup here
            }
        }
        
        stage('Run Katalon KRE Tests') {
            steps {
                script {
                    katalonKREPipeline(
                        'katalon-regression-tests',
                        env.GIT_URL,  // Use the current job's Git URL
                        '<EMAIL>'
                    )
                }
            }
        }
        
        stage('Post-Test Analysis') {
            steps {
                echo 'Analyzing test results...'
                // Add any post-test analysis here
                // The test results will be in workspace/test-results/
                script {
                    if (fileExists('test-results/summary.txt')) {
                        def summary = readFile('test-results/summary.txt')
                        echo "Test Summary:\n${summary}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            // Archive test results
            archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
        }
    }
}
*/

// Example 3: Parameterized build
/*
pipeline {
    agent any
    
    parameters {
        choice(
            name: 'TEST_SUITE',
            choices: ['All Tests', 'Smoke Tests', 'Regression Tests'],
            description: 'Which test suite to run'
        )
        string(
            name: 'TEST_ENVIRONMENT',
            defaultValue: 'test',
            description: 'Target environment for testing'
        )
    }
    
    stages {
        stage('Run Selected Test Suite') {
            steps {
                script {
                    echo "Running ${params.TEST_SUITE} on ${params.TEST_ENVIRONMENT} environment"
                    
                    katalonKREPipeline(
                        "katalon-${params.TEST_SUITE.toLowerCase().replace(' ', '-')}",
                        env.GIT_URL,
                        '<EMAIL>'
                    )
                }
            }
        }
    }
}
*/
