// Katalon KRE Pipeline Test
// This Jenkinsfile uses the katalonKREPipeline from the vars/ folder

pipeline {
    agent any
    
    stages {
        stage('Katalon KRE Test Execution') {
            steps {
                script {
                    // Call the shared library pipeline
                    katalonKREPipeline(
                        'katalon-test-project',                                    // appName
                        'https://your-git-repo/katalon-test-project.git',         // gitUrl (replace with your actual repo)
                        '<EMAIL>'                                  // emailList (replace with your email)
                    )
                }
            }
        }
    }
    
    post {
        always {
            echo 'Katalon KRE Pipeline test completed'
        }
        success {
            echo 'Katalon KRE Pipeline test succeeded!'
        }
        failure {
            echo 'Katalon KRE Pipeline test failed!'
        }
    }
}
